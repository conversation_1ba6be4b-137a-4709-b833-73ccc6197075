#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片列表清理工具
功能：检查img.txt中的URL对应的文件是否已存在于img文件夹中，
如果存在则删除对应的行，并统计删除的总条数。

使用方法：
    python clean_downloaded_images.py

作者：AI Assistant
日期：2025-08-01
"""

import os
import urllib.parse
import shutil
import time
from datetime import datetime


def extract_filename_from_url(url):
    """
    从URL中提取文件名
    
    Args:
        url (str): 图片URL
        
    Returns:
        str: 文件名，如果提取失败返回空字符串
    """
    try:
        parsed_url = urllib.parse.urlparse(url.strip())
        filename = os.path.basename(parsed_url.path)
        return filename if filename else ""
    except Exception:
        return ""


def create_backup(file_path):
    """
    创建备份文件
    
    Args:
        file_path (str): 要备份的文件路径
        
    Returns:
        str: 备份文件路径，如果失败返回None
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(file_path, backup_path)
        return backup_path
    except Exception as e:
        print(f"警告：无法创建备份文件: {e}")
        return None


def get_existing_files(folder_path):
    """
    获取文件夹中所有文件的集合
    
    Args:
        folder_path (str): 文件夹路径
        
    Returns:
        set: 文件名集合
    """
    try:
        existing_files = set()
        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)
            if os.path.isfile(item_path):
                existing_files.add(item)
        return existing_files
    except Exception as e:
        print(f"错误：无法读取文件夹 {folder_path}: {e}")
        return set()


def clean_image_list(img_txt_path, img_folder_path):
    """
    清理图片列表，删除已存在文件对应的URL行
    
    Args:
        img_txt_path (str): img.txt文件路径
        img_folder_path (str): 图片文件夹路径
        
    Returns:
        tuple: (删除的行数, 剩余行数, 删除的文件列表)
    """
    # 检查文件和文件夹是否存在
    if not os.path.exists(img_txt_path):
        print(f"错误：文件 {img_txt_path} 不存在")
        return 0, 0, []
    
    if not os.path.exists(img_folder_path):
        print(f"错误：文件夹 {img_folder_path} 不存在")
        return 0, 0, []
    
    # 创建备份文件
    backup_path = create_backup(img_txt_path)
    if backup_path:
        print(f"已创建备份文件: {backup_path}")
    
    # 读取所有行
    try:
        with open(img_txt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"错误：无法读取文件 {img_txt_path}: {e}")
        return 0, 0, []
    
    print(f"原始文件共有 {len(lines)} 行")
    
    # 获取img文件夹中所有文件的集合
    existing_files = get_existing_files(img_folder_path)
    print(f"img文件夹中共有 {len(existing_files)} 个文件")
    
    if not existing_files:
        print("img文件夹中没有文件，无需清理")
        return 0, len(lines), []
    
    # 过滤掉已存在文件对应的行
    remaining_lines = []
    deleted_count = 0
    deleted_files = []
    
    print("\n开始处理...")
    start_time = time.time()
    
    for line_num, line in enumerate(lines, 1):
        # 显示进度
        if line_num % 10000 == 0:
            progress = (line_num / len(lines)) * 100
            print(f"处理进度: {line_num}/{len(lines)} ({progress:.1f}%)")
        
        original_line = line
        line = line.strip()
        
        if not line:  # 保留空行
            remaining_lines.append(original_line)
            continue
            
        # 提取文件名
        filename = extract_filename_from_url(line)
        
        if not filename:
            # 无法提取文件名的行保留
            remaining_lines.append(original_line)
            continue
        
        # 检查文件是否存在
        if filename in existing_files:
            deleted_count += 1
            deleted_files.append(filename)
            # 只在文件数量较少时显示详细信息
            if len(existing_files) <= 100:
                print(f"删除第 {line_num} 行（文件已存在）: {filename}")
        else:
            remaining_lines.append(original_line)
    
    # 写回文件
    try:
        with open(img_txt_path, 'w', encoding='utf-8') as f:
            f.writelines(remaining_lines)
    except Exception as e:
        print(f"错误：无法写入文件 {img_txt_path}: {e}")
        return 0, 0, []
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"\n处理完成，耗时 {processing_time:.2f} 秒")
    
    return deleted_count, len(remaining_lines), deleted_files


def main():
    """主函数"""
    print("=" * 50)
    print("图片列表清理工具")
    print("=" * 50)
    
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    img_txt_path = os.path.join(current_dir,  'img', 'img.txt')
    img_folder_path = os.path.join(current_dir, 'img')
    
    print(f"img.txt 路径: {img_txt_path}")
    print(f"图片文件夹路径: {img_folder_path}")
    print()
    
    # 执行清理
    deleted_count, remaining_count, deleted_files = clean_image_list(img_txt_path, img_folder_path)
    
    # 显示结果
    print("=" * 50)
    print("清理结果")
    print("=" * 50)
    print(f"删除的行数: {deleted_count}")
    print(f"剩余的行数: {remaining_count}")
    print(f"总共删除了 {deleted_count} 条记录")
    
    # 显示删除的文件列表
    if deleted_files:
        print(f"\n删除的文件:")
        if len(deleted_files) <= 20:
            for i, filename in enumerate(deleted_files, 1):
                print(f"  {i:2d}. {filename}")
        else:
            for i, filename in enumerate(deleted_files[:10], 1):
                print(f"  {i:2d}. {filename}")
            print(f"  ... 还有 {len(deleted_files) - 10} 个文件")
    
    print("\n清理完成！")


if __name__ == "__main__":
    main()
