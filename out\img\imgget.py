#!/usr/bin/python
# coding=utf8
import html
import requests
import ssl
import json
import random
import time
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import partial
ssl._create_default_https_context = ssl._create_unverified_context
requests.packages.urllib3.disable_warnings()
THREAD_COUNT = 3  
MAX_RETRIES = 3   
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filename='1.log',
    filemode='w'
)
console = logging.StreamHandler()
console.setLevel(logging.ERROR) 
logging.getLogger('').addHandler(console)
USER_AGENTS = [
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36"
]

def get_random_headers():
    return {
        'User-Agent': random.choice(USER_AGENTS),
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Connection': 'keep-alive'
    }

def download_url(url, retry_count=0):
    url = url.strip()
    try:
        logging.info(f"开始处理: {url}")
        headers = get_random_headers()
        pngresponse = requests.get(
            url=url, 
            verify=False,
            headers=headers,
            timeout=10
        )
        pngresponse.raise_for_status()
        
        filename = url.split('/')[-1].split('?')[0]
        with open(filename, 'wb') as f:
            f.write(pngresponse.content)
        logging.info(f"成功下载: {url} -> {filename}")
        return True
    except Exception as e:
        if retry_count < MAX_RETRIES:
            logging.warning(f"处理URL出错(重试 {retry_count + 1}/{MAX_RETRIES}): {url} - 错误: {str(e)}")
            time.sleep(random.uniform(1, 3))
            return download_url(url, retry_count + 1)
        else:
            logging.error(f"处理URL失败(超过最大重试次数): {url} - 错误: {str(e)}")
            return False
def main():
    with open('img.txt', 'r') as url_file:
        urls = url_file.readlines()
    with ThreadPoolExecutor(max_workers=THREAD_COUNT) as executor:
        futures = {executor.submit(download_url, url): url for url in urls}
        
        for future in as_completed(futures):
            url = futures[future]
            try:
                future.result()
            except Exception as e:
                logging.error(f"未捕获的异常处理URL: {url} - 错误: {str(e)}")
if __name__ == "__main__":
    main()