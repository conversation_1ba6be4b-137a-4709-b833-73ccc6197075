#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理图片列表脚本
功能：检查img.txt中的URL对应的文件是否已存在于img文件夹中，
如果存在则删除对应的行，并统计删除的总条数。
"""

import os
import urllib.parse
from pathlib import Path


def extract_filename_from_url(url):
    """
    从URL中提取文件名
    
    Args:
        url (str): 图片URL
        
    Returns:
        str: 文件名
    """
    # 解析URL并获取路径部分
    parsed_url = urllib.parse.urlparse(url.strip())
    # 获取路径的最后一部分作为文件名
    filename = os.path.basename(parsed_url.path)
    return filename


def check_file_exists(img_folder, filename):
    """
    检查文件是否存在于指定文件夹中
    
    Args:
        img_folder (str): 图片文件夹路径
        filename (str): 文件名
        
    Returns:
        bool: 文件是否存在
    """
    file_path = os.path.join(img_folder, filename)
    return os.path.isfile(file_path)


def clean_img_list(img_txt_path, img_folder_path, backup=True):
    """
    清理图片列表，删除已存在文件对应的URL行

    Args:
        img_txt_path (str): img.txt文件路径
        img_folder_path (str): 图片文件夹路径
        backup (bool): 是否创建备份文件

    Returns:
        int: 删除的行数
    """
    if not os.path.exists(img_txt_path):
        print(f"错误：文件 {img_txt_path} 不存在")
        return 0

    if not os.path.exists(img_folder_path):
        print(f"错误：文件夹 {img_folder_path} 不存在")
        return 0

    # 创建备份文件
    if backup:
        backup_path = img_txt_path + '.backup'
        try:
            import shutil
            shutil.copy2(img_txt_path, backup_path)
            print(f"已创建备份文件: {backup_path}")
        except Exception as e:
            print(f"警告：无法创建备份文件: {e}")

    # 读取所有行
    try:
        with open(img_txt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"错误：无法读取文件 {img_txt_path}: {e}")
        return 0

    print(f"原始文件共有 {len(lines)} 行")

    # 获取img文件夹中所有文件的集合（提高查找效率）
    try:
        existing_files = set(os.listdir(img_folder_path))
        # 只保留文件，排除文件夹
        existing_files = {f for f in existing_files if os.path.isfile(os.path.join(img_folder_path, f))}
        print(f"img文件夹中共有 {len(existing_files)} 个文件")
    except Exception as e:
        print(f"错误：无法读取文件夹 {img_folder_path}: {e}")
        return 0

    # 过滤掉已存在文件对应的行
    remaining_lines = []
    deleted_count = 0
    deleted_files = []

    for line_num, line in enumerate(lines, 1):
        original_line = line
        line = line.strip()

        if not line:  # 跳过空行
            remaining_lines.append(original_line)
            continue

        # 提取文件名
        filename = extract_filename_from_url(line)

        if not filename:
            print(f"警告：第 {line_num} 行无法提取文件名: {line}")
            remaining_lines.append(original_line)
            continue

        # 检查文件是否存在（使用集合查找，效率更高）
        if filename in existing_files:
            print(f"删除第 {line_num} 行（文件已存在）: {filename}")
            deleted_count += 1
            deleted_files.append(filename)
        else:
            remaining_lines.append(original_line)

    # 写回文件
    try:
        with open(img_txt_path, 'w', encoding='utf-8') as f:
            f.writelines(remaining_lines)
    except Exception as e:
        print(f"错误：无法写入文件 {img_txt_path}: {e}")
        return 0

    print(f"\n处理完成！")
    print(f"删除了 {deleted_count} 行")
    print(f"剩余 {len(remaining_lines)} 行")

    # 显示删除的文件列表（如果数量不多的话）
    if deleted_files and len(deleted_files) <= 20:
        print(f"\n删除的文件列表:")
        for filename in deleted_files:
            print(f"  - {filename}")
    elif deleted_files:
        print(f"\n删除的文件太多，仅显示前10个:")
        for filename in deleted_files[:10]:
            print(f"  - {filename}")
        print(f"  ... 还有 {len(deleted_files) - 10} 个文件")

    return deleted_count


def main():
    """主函数"""
    import argparse

    # 命令行参数解析
    parser = argparse.ArgumentParser(description='清理图片列表，删除已存在文件对应的URL行')
    parser.add_argument('--img-txt', default='out/img/img.txt',
                       help='img.txt文件路径 (默认: out/img/img.txt)')
    parser.add_argument('--img-folder', default='out/img',
                       help='图片文件夹路径 (默认: out/img)')
    parser.add_argument('--no-backup', action='store_true',
                       help='不创建备份文件')

    args = parser.parse_args()

    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # 如果是相对路径，则相对于脚本所在目录
    if not os.path.isabs(args.img_txt):
        img_txt_path = os.path.join(current_dir, args.img_txt)
    else:
        img_txt_path = args.img_txt

    if not os.path.isabs(args.img_folder):
        img_folder_path = os.path.join(current_dir, args.img_folder)
    else:
        img_folder_path = args.img_folder

    print("=== 图片列表清理工具 ===")
    print(f"img.txt 路径: {img_txt_path}")
    print(f"图片文件夹路径: {img_folder_path}")
    print(f"备份设置: {'禁用' if args.no_backup else '启用'}")
    print()

    # 确认操作
    try:
        confirm = input("确认要执行清理操作吗？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("操作已取消")
            return
    except KeyboardInterrupt:
        print("\n操作已取消")
        return

    # 执行清理
    deleted_count = clean_img_list(img_txt_path, img_folder_path, backup=not args.no_backup)

    print(f"\n=== 清理完成 ===")
    print(f"总共删除了 {deleted_count} 条记录")


if __name__ == "__main__":
    main()
