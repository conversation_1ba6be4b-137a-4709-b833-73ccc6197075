#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版图片列表清理脚本
功能：检查img.txt中的URL对应的文件是否已存在于img文件夹中，
如果存在则删除对应的行，并统计删除的总条数。
"""

import os
import urllib.parse
import shutil


def extract_filename_from_url(url):
    """从URL中提取文件名"""
    parsed_url = urllib.parse.urlparse(url.strip())
    return os.path.basename(parsed_url.path)


def main():
    """主函数"""
    # 设置文件路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    img_txt_path = os.path.join(current_dir, 'out', 'img', 'img.txt')
    img_folder_path = os.path.join(current_dir, 'out', 'img')
    
    print("=== 图片列表清理工具 ===")
    print(f"img.txt 路径: {img_txt_path}")
    print(f"图片文件夹路径: {img_folder_path}")
    print()
    
    # 检查文件和文件夹是否存在
    if not os.path.exists(img_txt_path):
        print(f"错误：文件 {img_txt_path} 不存在")
        return
    
    if not os.path.exists(img_folder_path):
        print(f"错误：文件夹 {img_folder_path} 不存在")
        return
    
    # 创建备份文件
    backup_path = img_txt_path + '.backup'
    try:
        shutil.copy2(img_txt_path, backup_path)
        print(f"已创建备份文件: {backup_path}")
    except Exception as e:
        print(f"警告：无法创建备份文件: {e}")
    
    # 读取所有行
    try:
        with open(img_txt_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except Exception as e:
        print(f"错误：无法读取文件: {e}")
        return
    
    print(f"原始文件共有 {len(lines)} 行")
    
    # 获取img文件夹中所有文件的集合
    try:
        existing_files = set()
        for item in os.listdir(img_folder_path):
            item_path = os.path.join(img_folder_path, item)
            if os.path.isfile(item_path):
                existing_files.add(item)
        print(f"img文件夹中共有 {len(existing_files)} 个文件")
    except Exception as e:
        print(f"错误：无法读取文件夹: {e}")
        return
    
    # 过滤掉已存在文件对应的行
    remaining_lines = []
    deleted_count = 0
    deleted_files = []
    
    for line_num, line in enumerate(lines, 1):
        original_line = line
        line = line.strip()
        
        if not line:  # 保留空行
            remaining_lines.append(original_line)
            continue
            
        # 提取文件名
        filename = extract_filename_from_url(line)
        
        if not filename:
            print(f"警告：第 {line_num} 行无法提取文件名: {line}")
            remaining_lines.append(original_line)
            continue
        
        # 检查文件是否存在
        if filename in existing_files:
            print(f"删除第 {line_num} 行（文件已存在）: {filename}")
            deleted_count += 1
            deleted_files.append(filename)
        else:
            remaining_lines.append(original_line)
    
    # 写回文件
    try:
        with open(img_txt_path, 'w', encoding='utf-8') as f:
            f.writelines(remaining_lines)
    except Exception as e:
        print(f"错误：无法写入文件: {e}")
        return
    
    print(f"\n=== 处理完成 ===")
    print(f"删除了 {deleted_count} 行")
    print(f"剩余 {len(remaining_lines)} 行")
    print(f"总共删除了 {deleted_count} 条记录")
    
    # 显示部分删除的文件列表
    if deleted_files:
        if len(deleted_files) <= 10:
            print(f"\n删除的文件列表:")
            for filename in deleted_files:
                print(f"  - {filename}")
        else:
            print(f"\n删除的文件列表（前10个）:")
            for filename in deleted_files[:10]:
                print(f"  - {filename}")
            print(f"  ... 还有 {len(deleted_files) - 10} 个文件")


if __name__ == "__main__":
    main()
